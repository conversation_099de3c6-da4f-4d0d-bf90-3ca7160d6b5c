import { CDNImage } from '@/components/ui/CDNImage'
// import { useTranslation } from 'react-i18next'
import { useAtomValue } from 'jotai'
import { screenOrientationAtom } from '@/stores'
import { ScreenOrientation } from '@/stores/types'
import { publicPreLoadSourceObj } from '@/configs/source'
import { isIPad } from '@/utils'

export const PhotoMask: React.FC<{
  multiple: boolean
  className?: string
  direction?: ScreenOrientation
}> = ({ multiple, className }) => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  // const { t } = useTranslation()
  return (
    <>
      <CDNImage
        className={`absolute left-0 bottom-0 right-0 top-0 rounded-none object-cover ${className}`}
        src={
          multiple
            ? screenOrientation.isLandScape
              ? publicPreLoadSourceObj.photoMaskCouple
              : isIPad()
                ? publicPreLoadSourceObj.photoMaskIpad
                : publicPreLoadSourceObj.photoMaskCoupleV
            : screenOrientation.isLandScape
              ? '/images/photo/mask.png'
              : '/images/photo/mask-v.png'
        }
      />
    </>
  )
}
