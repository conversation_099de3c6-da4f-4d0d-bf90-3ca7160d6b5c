import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { useAtomValue, useSetAtom } from 'jotai'

import { screenOrientationAtom, currentThemeTypeAtom } from '@/stores'
import { publicPreLoadSourceObj } from '@/configs/source'

import { SvgIcon } from '@/components/ui/SvgIcon'
import { ActiveTemplateItem } from './const'
import { useMemo, useEffect } from 'react'
import { ThemeType } from '@/apis/types'

export const ShootTipModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  themeDetail: ActiveTemplateItem
}> = ({ open, setOpen, themeDetail }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const setCurrentThemeType = useSetAtom(currentThemeTypeAtom)

  const onCloseQuestion = () => {
    setOpen?.(false)
    navigate('/photo')
  }

  const curThemeType = useMemo(() => {
    const item = themeDetail.itemList?.[0]
    const fitNumber = item.fitNumber
    if (fitNumber === 'TWO') {
      return ThemeType.couple
    } else if (item.populationV2.includes('CHILD')) {
      return ThemeType.child
    } else {
      return item.sex === 'MALE' ? ThemeType.male : ThemeType.female
    }
  }, [themeDetail])

  useEffect(() => {
    setCurrentThemeType(curThemeType)
  }, [curThemeType])

  const colors = {
    male: {
      text: '男性',
      color: '#608CF3',
      bg: 'linear-gradient(180deg, rgba(42, 58, 122, 0.60) 0%, rgba(129, 198, 202, 0.60) 100%)',
    },
    female: {
      text: '女性',
      color: '#F08D99',
      bg: 'linear-gradient(180deg, rgba(116, 42, 122, 0.60) 0%, rgba(236, 166, 194, 0.60) 100%)',
    },
    child: {
      text: '儿童',
      color: '#FFD041',
      bg: 'linear-gradient(180deg, rgba(203, 99, 51, 0.60) 0%, rgba(233, 229, 116, 0.60) 100%)',
    },
    couple: {
      text: '双人',
      color: '#3ABDB5',
      bg: 'linear-gradient(180deg, rgba(87, 229, 239, 0.60) 0%, rgba(18, 166, 127, 0.60) 100%)',
    },
  }

  const tips = {
    male: [
      {
        icon: publicPreLoadSourceObj.guideMaleYes,
        text: t('正对镜头'),
        tipIcon: '/images/photo/correct.svg',
      },
      {
        icon: publicPreLoadSourceObj.guideMaleNo,
        text: t('移除遮挡'),
        tipIcon: '/images/photo/error.svg',
      },
      {
        icon: publicPreLoadSourceObj.guideLight,
        text: t('光线充足'),
      },
    ],
    female: [
      {
        icon: publicPreLoadSourceObj.guideFemaleYes,
        text: t('正对镜头'),
        tipIcon: '/images/photo/correct.svg',
      },
      {
        icon: publicPreLoadSourceObj.guideFemaleNo,
        text: t('移除遮挡'),
        tipIcon: '/images/photo/error.svg',
      },
      {
        icon: publicPreLoadSourceObj.guideLight,
        text: t('光线充足'),
      },
    ],
    child: [
      {
        icon: publicPreLoadSourceObj.guideChildYes,
        text: t('正对镜头'),
        tipIcon: '/images/photo/correct.svg',
      },
      {
        icon: publicPreLoadSourceObj.guideChildNo,
        text: t('移除遮挡'),
        tipIcon: '/images/photo/error.svg',
      },
      {
        icon: publicPreLoadSourceObj.guideLight,
        text: t('光线充足'),
      },
    ],
    couple: [
      {
        icon: publicPreLoadSourceObj.guideCoupleYes,
        text: t('正对镜头'),
        tipIcon: '/images/photo/correct.svg',
      },
      {
        icon: publicPreLoadSourceObj.guideCoupleNo,
        text: t('移除遮挡'),
        tipIcon: '/images/photo/error.svg',
      },
      {
        icon: publicPreLoadSourceObj.guideLight,
        text: t('光线充足'),
      },
    ],
  }

  return (
    <MyModal
      open={open}
      width={screenOrientation.isLandScape ? 900 : '90vw'}
      content={
        <div className="flex-1 pt-2">
          <h1 className="text-center text-[2.8rem] font-semibold">
            <span style={{ color: colors[curThemeType].color }}>
              {t(colors[curThemeType].text)}{' '}
            </span>
            <span>{t('模板')}</span>
          </h1>
          <p className="text-center text-[2.32rem] font-semibold mt-14 mb-20">
            For {t(colors[curThemeType].text)} - follow the tips
          </p>
          <div className="flex items-center w-full gap-5">
            {tips[curThemeType].map((tip, index) => (
              <div
                key={index}
                className="flex-1 flex flex-col py-10 h-[30rem] gap-1 items-center text-center border-2 rounded-[2.25rem] relative"
                style={{
                  background: colors[curThemeType].bg,
                }}
              >
                <SvgIcon src={tip.icon} alt="" className="w-[60%]" />
                <p className="h-[8rem] text-[2rem] leading-[2.4rem] text-center font-semibold maze-primary-text phone:text-[12px] phone:leading-[18px]">
                  {tip.text}
                </p>
                {tip.tipIcon && (
                  <SvgIcon
                    src={tip.tipIcon}
                    alt="提示图标"
                    className="w-16 h-16 absolute top-[0.5rem] right-[0.5rem] scale-[66%]"
                  />
                )}
              </div>
            ))}
          </div>
          <p className="px-3 py-6 my-12 text-[1.6rem] bg-white/20 rounded-sm text-center leading-[2.6rem]">
            {t('请确认以上信息后，点击“继续”开始拍摄')}
          </p>
        </div>
      }
      onOk={onCloseQuestion}
      okText={t('继续')}
      showCancelButton={false}
      contentClassName="p-0 w-full"
      onCancel={() => setOpen?.(false)}
    />
  )
}
