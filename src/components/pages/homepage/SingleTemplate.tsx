import classnames from 'classnames'
import { MyImage } from '@/components/ui/MyImage'
import classNames from 'classnames'
import styles from './SingleTemplate.module.css'
import { ThemeDetail } from '@/apis/types'
import { useAtomValue } from 'jotai'
import { screenOrientationAtom } from '@/stores'

/** 单个模版 */
function SingleTemplate({
  item,
  active = false,
  onSelect,
  isMultiple = false,
  className,
}: {
  item: ThemeDetail
  active: boolean
  isMultiple?: boolean
  className?: string
  activeGender?: string
  onSelect: () => void
}) {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  return (
    <div
      className={classnames(
        className,
        styles.template,
        isMultiple && styles.multiple,
        // 'flex flex-col cursor-pointer',
        {
          [styles.active]: active,
        }
      )}
      onClick={onSelect}
    >
      {item.name && (
        <div className="maze-theme-title-bg w-full h-[7.75rem] text-[2.5rem] leading-[7.75rem] font-semibold px-[1.2rem] rounded-b-[3rem] text-white absolute left-0 right-0 bottom-0 text-center text-ellipsis text-nowrap overflow-hidden ipad:h-[5.38rem] ipad:rounded-b-[2rem] ipad:leading-[5.38rem] ipad:text-[2rem]">
          {item.name}
        </div>
      )}
      <MyImage
        src={item?.cover_image || item?.cover_image_female}
        tag="v800"
        className={classNames('rounded-[3rem] ipad:rounded-[2rem] ', [
          screenOrientation.isLandScape
            ? 'h-[12.23vh]'
            : 'h-[36.28vh] ipad:h-[32vh] phone:h-[36.28dvh]',
        ])}
        imgClassName="object-cover"
      />
      {/* <div
        className={classnames(
          'font-bold text-xl mt-4 line-clamp-1 text-center',
          {
            'text-neutral-50': !active,
            'text-gradient-primary': active,
          }
        )}
      >
        {item?.name}
      </div> */}
    </div>
  )
}

export default SingleTemplate
