import { useState, useEffect, useMemo, useRef } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useAtom, useAtomValue } from 'jotai'
import useSwr from 'swr'
import { useAiTask } from '@/components/pages/photo/useAiTask'
import { MyMirrorAiTask } from '@/stores/types'
import { AiTaskDetailType } from '@/graphqls/types'
import { ThemeDetail } from '@/apis/types'
import {
  resultOrder<PERSON>tom,
  selectedEventIdAtom,
  selectedImageFrameAtom,
  selectedThemeDetailAtom,
} from '@/stores'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'

export interface ImageDataState {
  taskBaseIds: number[]
  imageIds: { [taskBaseId: number]: number[] }
  images: MyMirrorAiTask[]
}

export interface UseImageDataReturn {
  // 状态
  selectedImageData: ImageDataState
  activeImageIndex: number
  curImg: MyMirrorAiTask | null
  curVideo: any
  curMaterialId: { material_id: number; image_id: number }[] | null
  curMaterialDetail: any
  curMazeMaterialImageUrl: string | undefined
  imageTemplates: (ThemeDetail & { cover_video?: string | null })[]
  imagesCount: number

  // 方法
  setActiveImageIndex: (index: number) => void
  setActiveTemplate: (
    value: React.SetStateAction<
      (ThemeDetail & { cover_video?: string | null }) | null | undefined
    >
  ) => void
  mutateMateriaDetail: () => void
  storeImageToMaze: (
    images: any,
    event_id: number,
    selectedFrame: any
  ) => Promise<void>
}

export const useImageData = (): UseImageDataReturn => {
  // 状态管理
  const [selectedImageData, setSelectedImageData] = useState<ImageDataState>({
    taskBaseIds: [],
    imageIds: {},
    images: [],
  })
  const [activeImageIndex, setActiveImageIndex] = useState(0)
  const [curMaterialId, setCurMaterialId] = useState<
    { material_id: number; image_id: number }[] | null
  >(null)

  // 标记是否已存储图片，避免重复调用
  const hasStoredImages = useRef(false)

  // Atoms
  const [, setResultOrder] = useAtom(resultOrderAtom)
  const [selectedEventId] = useAtom(selectedEventIdAtom)
  const [selectedFrame] = useAtom(selectedImageFrameAtom)
  const selectedThemeDetail = useAtomValue(selectedThemeDetailAtom)

  // URL参数
  const [searchParams] = useSearchParams()
  const imagesParam = searchParams.get('images')

  // 计算图片总数
  const imagesCount = useMemo(() => {
    if (!imagesParam) return 0
    let totalCount = 0
    imagesParam.split(';').forEach(taskImagePair => {
      const [, imageIdsStr] = taskImagePair.split(':')
      if (imageIdsStr) {
        totalCount += imageIdsStr.split(',').length
      }
    })
    return totalCount
  }, [imagesParam])

  // 当前活跃图片
  const curImg = useMemo(() => {
    return selectedImageData.images[activeImageIndex] || null
  }, [selectedImageData.images, activeImageIndex])

  const curVideo = curImg?.video

  // 获取图片边框合成结果，轮询
  const { data: materiaDetail, mutate: mutateMateriaDetail } = useSwr(
    curMaterialId ? [curMaterialId] : null,
    ([material_ids]) =>
      _ajax.post(_api.get_image, {
        material_ids: material_ids?.map(it => it.material_id),
      }),
    {
      refreshInterval: data => {
        const items = data?.data?.data
        const isAllImageCompleted =
          Array.isArray(items) &&
          items.length > 0 &&
          items.every((it: any) => it.url)
        if (isAllImageCompleted) {
          return 0 // 停止轮询
        } else {
          return 3_000
        }
      },
    }
  )

  const curMaterialDetail = useMemo(() => {
    return materiaDetail?.data?.data?.find(
      (it: any) => it.wj_image_id === curImg?.id
    )
  }, [materiaDetail, curImg])

  const curMazeMaterialImageUrl = useMemo(() => {
    return curMaterialDetail?.url
  }, [curMaterialDetail])

  // AI任务轮询
  const { pollAiTaskStatus, stopPollAiTaskStatus } = useAiTask()

  // 解析URL参数并获取图片数据
  useEffect(() => {
    if (!imagesParam) return

    try {
      // 解析图片参数格式: taskId1:imageId1,imageId2;taskId2:imageId3,imageId4
      const taskImageMap: { [taskBaseId: number]: number[] } = {}
      const taskBaseIds: number[] = []

      imagesParam.split(';').forEach(taskImagePair => {
        const [taskId, imageIdsStr] = taskImagePair.split(':')
        const taskBaseId = Number(taskId)
        const imageIds = imageIdsStr.split(',').map(Number)

        taskImageMap[taskBaseId] = imageIds
        taskBaseIds.push(taskBaseId)
      })

      setSelectedImageData(prev => ({
        ...prev,
        taskBaseIds,
        imageIds: taskImageMap,
      }))

      // 停止现有轮询
      stopPollAiTaskStatus()

      // 创建统一的轮询函数处理所有taskBaseIds
      const pollAllTasks = () => {
        Promise.all(
          taskBaseIds.map(
            taskBaseId =>
              new Promise(resolve => {
                pollAiTaskStatus({
                  taskBaseId,
                  onProgress({ taskList }) {
                    updateImagesForTask(taskBaseId, taskList)
                    resolve(null)
                  },
                  onSuccess({ order, taskList }) {
                    updateImagesForTask(taskBaseId, taskList)
                    setResultOrder(order)
                    resolve(null)
                  },
                  onFail() {
                    console.error(`获取图片失败，taskBaseId: ${taskBaseId}`)
                    resolve(null)
                  },
                })
              })
          )
        ).catch(err => {
          console.error('轮询任务出错:', err)
        })
      }

      // 开始轮询
      pollAllTasks()
    } catch (error) {
      console.error('解析图片参数出错:', error)
    }
  }, [imagesParam])

  // 更新特定taskBaseId的图片数据
  const updateImagesForTask = (
    taskBaseId: number,
    taskList: MyMirrorAiTask[]
  ) => {
    console.log('更新任务图片', taskBaseId, taskList)
    setSelectedImageData(prev => {
      // 只包含DRAW类型的结果
      const drawImages = taskList.filter(
        img => img.detailType === AiTaskDetailType.DRAW
      )
      // 获取视频结果
      const videoImages = taskList.filter(
        img => img.detailType === AiTaskDetailType.VIDEO
      )
      const resultImages = drawImages?.map(item => ({
        ...item,
        video: videoImages?.[0],
      }))

      return {
        ...prev,
        images: [...prev.images, ...resultImages],
      }
    })
  }

  // 存储图片到Maze
  const storeImageToMaze = async (
    images: any,
    event_id: number,
    selectedFrame: any
  ) => {
    // 检查所有图片是否都有resultUrl
    const allImagesHaveUrls = images.every((it: any) => it.resultUrl)

    if (!allImagesHaveUrls) {
      console.log('不是所有图片都有URL，稍后处理完成时再存储')
      return
    }

    // 重试机制
    const maxRetries = 3
    let retryCount = 0
    let success = false

    while (!success && retryCount < maxRetries) {
      try {
        const res = await _ajax.post(_api.store_image, {
          event_id,
          theme_id: selectedThemeDetail?.id,
          ...(selectedFrame?.id ? { frame_id: selectedFrame.id } : {}),
          images: images.map((it: any) => {
            const isVideo = !!it?.video
            return {
              image_id: it.id,
              url: isVideo ? it.video.resultUrl : it.resultUrl,
              cover_image_url: isVideo ? it.resultUrl : null,
              type: isVideo ? 'video' : 'image',
            }
          }),
        })
        const materialId = res.data?.data?.images
        if (materialId) {
          setCurMaterialId(materialId)
          success = true
        } else {
          retryCount++
          console.warn(`未返回材料ID，重试 ${retryCount}/${maxRetries}`)
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      } catch (error) {
        retryCount++
        console.error(`存储图片出错 (尝试 ${retryCount}/${maxRetries}):`, error)

        if (retryCount >= maxRetries) {
          console.error('达到最大重试次数，放弃')
          hasStoredImages.current = false
        } else {
          await new Promise(resolve =>
            setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1))
          )
        }
      }
    }
  }

  // 当图片数据完整时自动存储
  useEffect(() => {
    if (
      selectedImageData.images.length === imagesCount &&
      selectedEventId &&
      !hasStoredImages.current
    ) {
      storeImageToMaze(selectedImageData.images, selectedEventId, selectedFrame)
      hasStoredImages.current = true
    }
  }, [selectedImageData, selectedEventId, selectedFrame, imagesCount])

  // 当materiaDetail变化时更新图片URL
  useEffect(() => {
    if (materiaDetail?.data?.data && selectedImageData.images.length > 0) {
      interface ProcessedImage {
        wj_image_id: number
        url: string
        id: number
        material_id: number
        cover_image_url: string
      }

      const processedImages = materiaDetail.data.data as ProcessedImage[]
      const currentImageUrls = new Map(
        selectedImageData.images.map(img => [img.id, img.resultUrl])
      )

      const imagesToUpdate: { id: number; url: string }[] = []

      processedImages.forEach((processedImg: ProcessedImage) => {
        const currentUrl = currentImageUrls.get(processedImg.wj_image_id)
        if (
          processedImg.cover_image_url &&
          processedImg.url &&
          processedImg.wj_image_id &&
          currentUrl !== processedImg.url
        ) {
          imagesToUpdate.push({
            id: processedImg.wj_image_id,
            url: processedImg.url,
          })
        }
      })

      if (imagesToUpdate.length > 0) {
        setSelectedImageData(prev => ({
          ...prev,
          images: prev.images.map(img => {
            const updateInfo = imagesToUpdate.find(
              update => update.id === img.id
            )
            if (updateInfo) {
              return { ...img, resultUrl: updateInfo.url }
            }
            return img
          }),
        }))
      }
    }
  }, [materiaDetail])

  // 转换为MazeSingleTemplateList期望的格式
  const imageTemplates = useMemo(() => {
    const baseTemplates = selectedImageData.images.map(img => ({
      id: img.id,
      cover_image: img.resultUrl || img.editResultUrls?.[2],
      cover_image_female: img.resultUrl || img.editResultUrls?.[2],
      video: (img as any).video || undefined,
      template_count: 0,
      name: '',
      type: 0 as 0 | 1,
      price: 0,
    })) as (ThemeDetail & { cover_video?: string | null })[]

    // 如果图片数量在1-4之间，重复图片以支持loop模式
    if (baseTemplates.length > 1 && baseTemplates.length < 5) {
      const extendedTemplates = [...baseTemplates]
      while (extendedTemplates.length < 5) {
        const remainingSlots = 5 - extendedTemplates.length
        const templatesToAdd = baseTemplates.slice(
          0,
          Math.min(remainingSlots, baseTemplates.length)
        )
        extendedTemplates.push(...templatesToAdd)
      }
      return extendedTemplates
    }

    return baseTemplates
  }, [selectedImageData.images])

  // 创建setActiveTemplate函数
  const setActiveTemplate = useMemo(() => {
    return (
      value: React.SetStateAction<
        (ThemeDetail & { cover_video?: string | null }) | null | undefined
      >
    ) => {
      if (typeof value === 'function') {
        const currentTemplate = imageTemplates[activeImageIndex]
        const newTemplate = value(currentTemplate)
        if (!newTemplate) return

        const originalIndex = selectedImageData.images.findIndex(
          img => img.id === newTemplate.id
        )
        if (originalIndex !== -1) {
          setActiveImageIndex(originalIndex)
        }
      } else if (value) {
        const originalIndex = selectedImageData.images.findIndex(
          img => img.id === value.id
        )
        if (originalIndex !== -1) {
          setActiveImageIndex(originalIndex)
        }
      }
    }
  }, [imageTemplates, activeImageIndex, selectedImageData.images])

  // 清理函数
  useEffect(() => {
    return () => {
      stopPollAiTaskStatus()
      hasStoredImages.current = false
    }
  }, [])

  return {
    // 状态
    selectedImageData,
    activeImageIndex,
    curImg,
    curVideo,
    curMaterialId,
    curMaterialDetail,
    curMazeMaterialImageUrl,
    imageTemplates,
    imagesCount,

    // 方法
    setActiveImageIndex,
    setActiveTemplate,
    mutateMateriaDetail,
    storeImageToMaze,
  }
}
