import { useState, useEffect } from 'react'
import { composeSignatureImage } from '@/utils/signatureComposer'
import { sleep } from '@/utils'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'

export interface SignatureData {
  imageData: string
  position: 'left-bottom' | 'right-bottom'
  size: 'small' | 'large'
}

export interface UseSignatureProps {
  curMaterialDetail?: {
    id: string
    url: string
  }
  mutateMateriaDetail: () => void
}

export interface UseSignatureReturn {
  signatureData: SignatureData | null
  handleSignatureComplete: (data: SignatureData) => Promise<void>
}

/**
 * 管理签名相关的所有业务逻辑
 */
export const useSignature = (props: UseSignatureProps): UseSignatureReturn => {
  const { curMaterialDetail, mutateMateriaDetail } = props
  
  const [signatureData, setSignatureData] = useState<SignatureData | null>(null)
  
  // 上传签名图片
  const uploadSignatureImage = async (file: Blob, materialId: string) => {
    const formData = new FormData()
    const fileName = `signature_${materialId}_${Date.now()}.png`
    formData.append('file', file, fileName)

    return _ajax.post(_api.upload_signature_image, formData, {
      params: { material_id: materialId },
    })
  }
  
  // 处理签名完成
  const handleSignatureComplete = async (data: SignatureData) => {
    try {
      // 清除之前的处理记录，确保新签名可以应用到所有图片
      sessionStorage.removeItem('lastProcessedSignatureImageId')

      // 等待curMaterialDetail.url可用
      if (!curMaterialDetail?.url) {
        console.error('背景图片未加载完成')
        return
      }

      // 合成图片
      const composedImage = await composeSignatureImage({
        backgroundImageUrl: curMaterialDetail.url,
        signatureImageData: data.imageData,
        position: data.position,
        size: data.size,
      })

      // 上传图片
      await uploadSignatureImage(composedImage, curMaterialDetail.id)

      // 记录当前图片已处理
      sessionStorage.setItem(
        'lastProcessedSignatureImageId',
        curMaterialDetail.id
      )

      await sleep(1000)
      await mutateMateriaDetail()
      
      // 保存签名数据用于显示
      setSignatureData(data)
      
      console.log('签名处理成功')
    } catch (error) {
      console.error('签名处理失败:', error)
    }
  }
  
  // 切换图片时，如果有签名数据则自动合成和上传
  useEffect(() => {
    const autoComposeAndUpload = async () => {
      if (signatureData && curMaterialDetail?.url && curMaterialDetail?.id) {
        // 检查是否已经为当前图片处理过签名，避免重复处理
        const currentImageId = curMaterialDetail.id
        const lastProcessedImageId = sessionStorage.getItem(
          'lastProcessedSignatureImageId'
        )

        if (lastProcessedImageId === currentImageId) {
          console.log('当前图片已处理过签名，跳过')
          return
        }

        try {
          console.log('检测到图片切换，开始自动合成签名...')

          // 合成图片
          const composedImage = await composeSignatureImage({
            backgroundImageUrl: curMaterialDetail.url,
            signatureImageData: signatureData.imageData,
            position: signatureData.position,
            size: signatureData.size,
          })

          // 上传图片
          await uploadSignatureImage(composedImage, curMaterialDetail.id)

          // 记录已处理的图片ID
          sessionStorage.setItem(
            'lastProcessedSignatureImageId',
            currentImageId
          )

          await sleep(1000)
          await mutateMateriaDetail()

          console.log('图片切换时签名合成上传成功')
        } catch (error) {
          console.error('图片切换时签名处理失败:', error)
        }
      }
    }

    autoComposeAndUpload()
  }, [curMaterialDetail?.id, signatureData, curMaterialDetail?.url, mutateMateriaDetail])
  
  return {
    signatureData,
    handleSignatureComplete,
  }
}
