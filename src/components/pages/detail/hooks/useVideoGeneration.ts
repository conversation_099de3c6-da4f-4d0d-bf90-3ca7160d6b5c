import { useState, useEffect } from 'react'
import { useAtom, useAtomValue } from 'jotai'
import useSwr from 'swr'
import { toast } from '@/components/ui/shad/use-toast'
import { selectedEventIdAtom, selectedThemeDetailAtom } from '@/stores'
import { ShortsStatusEnum } from '@/apis/types'
import { MyMirrorAiTask } from '@/stores/types'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'

export interface ShortsData {
  id: string
  resultUrl: string
  status: ShortsStatusEnum
  job_id?: string
  origin_url?: string
  url?: string
}

export interface UseVideoGenerationProps {
  selectedImageData: {
    images: MyMirrorAiTask[]
  }
  activeImageIndex: number
}

export interface UseVideoGenerationReturn {
  shorts: ShortsData | null
  generateShortsPending: boolean
  handleGenerateShorts: () => Promise<void>
}

/**
 * 管理视频生成和轮询相关逻辑
 */
export const useVideoGeneration = (props: UseVideoGenerationProps): UseVideoGenerationReturn => {
  const { selectedImageData, activeImageIndex } = props
  
  const [shorts, setShorts] = useState<ShortsData | null>(null)
  const [generateShortsPending, setGenerateShortsPending] = useState<boolean>(false)
  
  const [selectedEventId] = useAtom(selectedEventIdAtom)
  const selectedThemeDetail = useAtomValue(selectedThemeDetailAtom)
  
  // 轮询视频生成状态
  const { data: shortsDetail } = useSwr(
    shorts?.job_id ? [shorts.job_id] : null,
    ([id]) =>
      _ajax.get(_api.video_status, {
        params: { job_id: id },
      }),
    {
      refreshInterval: () => {
        if (shorts?.status === ShortsStatusEnum.SUCCESS) {
          return 0 // 停止轮询
        }
        return 3_000
      },
    }
  )
  
  // 处理视频生成
  const handleGenerateShorts = async () => {
    if (shorts?.status === ShortsStatusEnum.SUCCESS) return
    
    setGenerateShortsPending(true)
    
    const startImg = selectedImageData.images[activeImageIndex]
    const endImg =
      selectedImageData.images[
        Math.floor(Math.random() * selectedImageData.images.length)
      ]
    
    // 如果选中了相同的图片，重新选择
    if (startImg.id === endImg.id) {
      console.log('选中了相同图片，重新尝试...')
      handleGenerateShorts()
      return
    }
    
    try {
      const res = await _ajax.post(_api.generate_video, {
        image_start_url: startImg.resultUrl,
        image_end_url: endImg.resultUrl,
        image_start_id: startImg.id,
        image_end_id: endImg.id,
        event_id: selectedEventId,
        theme_id: selectedThemeDetail?.id,
      })
      
      if (res.data?.code === 200) {
        setShorts(res.data.data)
      } else {
        setGenerateShortsPending(false)
        toast({
          description: res.data.msg || '视频生成失败',
        })
      }
    } catch (error) {
      setGenerateShortsPending(false)
      console.error('视频生成出错:', error)
      toast({
        description: '视频生成失败',
      })
    }
  }
  
  // 监听视频状态变化
  useEffect(() => {
    if (shortsDetail?.data?.data?.status === ShortsStatusEnum.SUCCESS) {
      setShorts(shortsDetail?.data?.data)
      setGenerateShortsPending(false)
    }
  }, [shortsDetail])
  
  // 组件卸载时清理状态
  useEffect(() => {
    return () => {
      setShorts(null)
      setGenerateShortsPending(false)
    }
  }, [])
  
  return {
    shorts,
    generateShortsPending,
    handleGenerateShorts,
  }
}
