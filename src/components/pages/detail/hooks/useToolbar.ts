import { useMemo } from 'react'
import { use<PERSON><PERSON>, useAtomValue } from 'jotai'
import { useTranslation } from 'react-i18next'
import { toast } from '@/components/ui/shad/use-toast'
import { useBridge } from '@/hooks/useBridge'
import { 
  printerEnableAtom, 
  machineInfoAtom, 
  selectedEventIdAtom,
  selectedEventDetailAtom 
} from '@/stores'
import { PrinterStatus } from '@/stores/types'
import { ShortsStatusEnum } from '@/apis/types'
import { publicPreLoadSourceObj } from '@/configs/source'
import { downloadRemoteFile, isIphone, isWebApp } from '@/utils'
import { ModalType } from './useModals'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'

export interface ToolbarItem {
  icon: string
  title: string
  op: ModalType | 'downloadShorts'
  disabled: boolean
  enable: boolean
}

export interface UseToolbarProps {
  curMazeMaterialImageUrl?: string
  curMaterialId: { material_id: number; image_id: number }[] | null
  curVideo?: any
  shorts?: {
    id: string
    resultUrl: string
    status: ShortsStatusEnum
    job_id?: string
    origin_url?: string
    url?: string
  } | null
  generateShortsPending: boolean
  onGenerateShorts: () => void
}

export interface UseToolbarReturn {
  toolbarList: ToolbarItem[]
  handleToolbarClick: (op: string) => void
}

/**
 * 管理工具栏配置和点击处理逻辑
 */
export const useToolbar = (
  props: UseToolbarProps,
  openModal: (modalType: ModalType) => void
): UseToolbarReturn => {
  const {
    curMazeMaterialImageUrl,
    curMaterialId,
    curVideo,
    shorts,
    generateShortsPending,
    onGenerateShorts,
  } = props
  
  const { t } = useTranslation()
  const { downImage } = useBridge()
  const [printerEnable] = useAtom(printerEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)
  const [selectedEventId] = useAtom(selectedEventIdAtom)
  const [selectedEventDetail] = useAtom(selectedEventDetailAtom)
  
  // 检查是否有可用打印机
  const hasPrinter = useMemo(() => {
    return (
      printerEnable &&
      (machineInfo?.printers?.some(
        it => it.printerStatus === PrinterStatus.AVAILABLE
      ) ||
        machineInfo?.printerStatus === PrinterStatus.AVAILABLE)
    )
  }, [printerEnable, machineInfo])
  
  // 工具栏配置
  const toolbarList = useMemo(() => {
    return isWebApp()
      ? [
          {
            icon: publicPreLoadSourceObj.detailDownload,
            title: t('下载'),
            op: 'download' as ModalType,
            disabled: !curMazeMaterialImageUrl,
            enable: selectedEventDetail?.enable_download,
          },
          {
            icon: publicPreLoadSourceObj.detailEmail,
            title: t('邮件'),
            op: 'email' as ModalType,
            disabled: curMaterialId === null,
            enable: selectedEventDetail?.enable_mail,
          },
          {
            icon: publicPreLoadSourceObj.detailPrint,
            title: t('打印'),
            op: 'scanQrPrint' as ModalType,
            disabled: false,
            enable: true,
          },
          {
            icon: publicPreLoadSourceObj.detailShare,
            title: t('分享'),
            op: 'share' as ModalType,
            disabled: false,
            enable: true,
          },
        ]
      : [
          {
            icon: publicPreLoadSourceObj.detailPrint,
            title: t('打印'),
            op: 'print' as ModalType,
            disabled: !hasPrinter || !curMazeMaterialImageUrl,
            enable: selectedEventDetail?.enable_print,
          },
          {
            icon: publicPreLoadSourceObj.detailEmail,
            title: t('邮件'),
            op: 'email' as ModalType,
            disabled: curMaterialId === null,
            enable: selectedEventDetail?.enable_mail,
          },
          {
            icon: publicPreLoadSourceObj.detailSign,
            title: t('签名'),
            op: 'signature' as ModalType,
            disabled: false,
            enable: true,
          },
          {
            icon: publicPreLoadSourceObj.detailDownload,
            title: t('下载'),
            op: 'download' as ModalType,
            disabled: !curMazeMaterialImageUrl,
            enable: selectedEventDetail?.enable_download,
          },
          {
            icon: publicPreLoadSourceObj.detailShorts,
            title:
              shorts?.status === ShortsStatusEnum.SUCCESS
                ? t('下载视频')
                : t(generateShortsPending ? '生成中' : '视频'),
            op:
              shorts?.status === ShortsStatusEnum.SUCCESS
                ? 'downloadShorts'
                : 'shorts' as ModalType,
            enable: selectedEventDetail?.enable_video,
            disabled: generateShortsPending,
          },
        ].filter((it: any) => it.enable)
  }, [
    selectedEventDetail,
    hasPrinter,
    curMazeMaterialImageUrl,
    curMaterialId,
    shorts,
    generateShortsPending,
    t,
  ])
  
  // 处理工具栏点击
  const handleToolbarClick = (op: string) => {
    if (op === 'download') {
      _ajax.post(_api.record_download, { event_id: selectedEventId })
      if (isWebApp()) {
        downloadRemoteFile(curVideo?.resultUrl || curMazeMaterialImageUrl)
        return
      }
      if (isIphone()) {
        downImage({
          imageUrls: [curMazeMaterialImageUrl],
        })
        toast({
          description: t('保存成功'),
        })
        return
      }
    }
    
    if (op === 'print') {
      _ajax.post(_api.record_print, { event_id: selectedEventId })
    }
    
    if (op === 'shorts') {
      onGenerateShorts()
    }
    
    if (op === 'downloadShorts') {
      // 下载视频的逻辑，打开下载模态框
      openModal('downloadShorts')
      return
    }
    
    // 其他操作直接打开对应模态框
    openModal(op as ModalType)
  }
  
  return {
    toolbarList,
    handleToolbarClick,
  }
}
