import { useState } from 'react'

export type ModalType = 
  | 'email'
  | 'download'
  | 'downloadShorts'
  | 'print'
  | 'share'
  | 'scanQrPrint'
  | 'shorts'
  | 'signature'
  | null

export interface UseModalsReturn {
  // 当前打开的模态框类型
  currentModal: ModalType
  
  // 模态框状态检查
  isModalOpen: (modalType: ModalType) => boolean
  
  // 模态框控制方法
  openModal: (modalType: ModalType) => void
  closeModal: () => void
  closeAllModals: () => void
}

/**
 * 统一管理所有模态框的开关状态
 * 替代原来分散的 curOptType 逻辑
 */
export const useModals = (): UseModalsReturn => {
  const [currentModal, setCurrentModal] = useState<ModalType>(null)
  
  // 检查特定模态框是否打开
  const isModalOpen = (modalType: ModalType): boolean => {
    return currentModal === modalType
  }
  
  // 打开指定模态框
  const openModal = (modalType: ModalType): void => {
    setCurrentModal(modalType)
  }
  
  // 关闭当前模态框
  const closeModal = (): void => {
    setCurrentModal(null)
  }
  
  // 关闭所有模态框（别名，与closeModal功能相同）
  const closeAllModals = (): void => {
    setCurrentModal(null)
  }
  
  return {
    currentModal,
    isModalOpen,
    openModal,
    closeModal,
    closeAllModals,
  }
}
