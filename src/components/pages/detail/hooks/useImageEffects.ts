import { useEffect, useRef, useState } from 'react'
import confetti, { CreateTypes } from 'canvas-confetti'
import { useDevice } from '@/hooks/useDevice'
import { VirtualInfo } from '@/apis/types'
import { MyMirrorAiTask } from '@/stores/types'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'

export interface UseImageEffectsProps {
  curImg: MyMirrorAiTask | null
}

export interface UseImageEffectsReturn {
  curVirtualInfo?: VirtualInfo
  isLikeBtnShow: boolean
  setIsLikeBtnShow: (show: boolean) => void
}

/**
 * 管理图片相关的特效和其他辅助功能
 */
export const useImageEffects = (props: UseImageEffectsProps): UseImageEffectsReturn => {
  const { curImg } = props
  
  const [curVirtualInfo, setCurVirtualInfo] = useState<VirtualInfo>()
  const [isLikeBtnShow, setIsLikeBtnShow] = useState(true)
  const confettiRef = useRef<CreateTypes>()
  const { getDefaultDeviceInfo } = useDevice()
  
  // 获取虚拟设备信息
  const getVirtualDevice = async () => {
    const deviceInfo = await getDefaultDeviceInfo()
    const res = await _ajax.post(_api.get_virtual_device, {
      device_id: deviceInfo?.id,
    })
    if (res.data?.code === 200) {
      setCurVirtualInfo(res.data.data)
    }
  }
  
  // 初始化时获取虚拟设备信息
  useEffect(() => {
    getVirtualDevice()
  }, [])
  
  // 动效处理
  useEffect(() => {
    if (curImg) {
      const canvas = document.getElementById('detail-canvas-container')
      if (!confettiRef.current) {
        confettiRef.current = confetti.create(canvas as HTMLCanvasElement, {
          resize: true,
        })
      }
      
      const count = 600
      const fire = (particleRatio: number, opts: any) => {
        confettiRef.current?.({
          ...opts,
          origin: { y: 0.68, x: 0.5 },
          colors: ['#bb0000', '#71bab8', '#fd97b0', '#fcb73f'],
          particleCount: Math.floor(count * particleRatio),
          scalar: (opts?.scalar ? opts?.scalar : 0) * 1.5,
          startVelocity: 60,
          shapes: ['square'],
        })
      }
      
      // 触发多层动效
      fire(0.25, {
        spread: 26,
        startVelocity: 55,
      })
      fire(0.2, {
        spread: 60,
      })
      fire(0.35, {
        spread: 100,
        decay: 0.91,
        scalar: 0.8,
      })
      fire(0.1, {
        spread: 120,
        startVelocity: 25,
        decay: 0.92,
        scalar: 1.2,
      })
      fire(0.1, {
        spread: 120,
        startVelocity: 45,
      })
      fire(0.5, {
        spread: 360,
        startVelocity: 45,
      })
    }
  }, [curImg])
  
  // 清理动效
  useEffect(() => {
    return () => {
      if (confettiRef.current) {
        confettiRef.current.reset()
      }
    }
  }, [])
  
  return {
    curVirtualInfo,
    isLikeBtnShow,
    setIsLikeBtnShow,
  }
}
