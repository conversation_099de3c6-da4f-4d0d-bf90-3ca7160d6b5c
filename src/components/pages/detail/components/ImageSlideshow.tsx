import { useAtomValue } from 'jotai'
import classNames from 'classnames'
import { screenOrientationAtom } from '@/stores'
import { ThemeDetail, ShortsStatusEnum } from '@/apis/types'
import { MyMirrorAiTask } from '@/stores/types'
import { SignatureData } from '../hooks/useSignature'
import { ShortsData } from '../hooks/useVideoGeneration'
import { isIPad, isPad } from '@/utils'
import { publicPreLoadSourceObj } from '@/configs/source'
import { MirrorLoading } from 'wujieai-react-icon'
import { SvgIcon } from '@/components/ui/SvgIcon'
import MazeSingleTemplateList from '@/components/pages/homepage/MazeSingleTemplateList'

export interface ImageSlideshowProps {
  // 图片数据
  imageTemplates: (ThemeDetail & { cover_video?: string | null })[]
  activeImageIndex: number
  curImg: MyMirrorAiTask | null
  
  // 模板设置函数
  setActiveTemplate: (
    value: React.SetStateAction<
      (ThemeDetail & { cover_video?: string | null }) | null | undefined
    >
  ) => void
  
  // 签名数据
  signatureData: SignatureData | null
  
  // 视频相关
  shorts: ShortsData | null
  generateShortsPending: boolean
  onShortsClick: () => void
}

/**
 * 图片轮播展示组件
 * 负责展示图片轮播、签名覆盖层、视频控制按钮等
 */
export const ImageSlideshow: React.FC<ImageSlideshowProps> = ({
  imageTemplates,
  activeImageIndex,
  curImg,
  setActiveTemplate,
  signatureData,
  shorts,
  generateShortsPending,
  onShortsClick,
}) => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  
  return (
    <div
      className={classNames(
        'h-[60vh] relative',
        screenOrientation.isLandScape
          ? 'w-[80vw] scale-[0.8]'
          : 'w-full scale-100'
      )}
    >
      {/* 图片轮播 */}
      <MazeSingleTemplateList
        selectTemplateList={imageTemplates}
        activeTemplate={imageTemplates[activeImageIndex]}
        setActiveTemplate={setActiveTemplate}
        listKey="detail-image-list"
        multiline={false}
        swiperProps={{
          slidesPerView: screenOrientation.isLandScape
            ? 3
            : isIPad()
              ? 1.62
              : isPad()
                ? 1.72
                : 1.32,
          loop: true,
          initialSlide: 0,
        }}
      />
      
      {/* 签名覆盖层 */}
      {signatureData && (
        <div
          className={classNames(
            'absolute z-20 pointer-events-none',
            {
              // 左下位置
              'bottom-4 left-[18dvw]':
                signatureData.position === 'left-bottom',
              // 右下位置
              'bottom-4 right-[18dvw]':
                signatureData.position === 'right-bottom',
              // 小尺寸
              'w-32 h-16': signatureData.size === 'small',
              // 大尺寸
              'w-44 h-20': signatureData.size === 'large',
            },
            screenOrientation.isLandScape ? 'scale-75' : ''
          )}
        >
          <img
            src={signatureData.imageData}
            alt="签名"
            className="w-full h-full object-cover"
          />
        </div>
      )}
      
      {/* 视频控制按钮 */}
      {shorts && (
        <div
          className={classNames(
            'absolute top-[6%] left-[26%] rounded-xl leading-none bg-[rgba(35,40,61,0.5)] shadow-[0_0_4.48px_0_rgba(0,0,0,0.4)] w-[3.125rem] h-[3.125rem] p-3 cursor-pointer phone:w-[5rem] phone:h-[5rem]',
            screenOrientation.isLandScape ? 'left-[37%] -top-[2%]' : ''
          )}
        >
          {generateShortsPending ||
          shorts?.status === ShortsStatusEnum.PENDING_QUEUE ? (
            <MirrorLoading className="w-full h-full absolute left-0 top-0 animate-spin maze-primary-text" />
          ) : (
            <SvgIcon
              src={publicPreLoadSourceObj.play}
              svgClassName="w-full h-full"
              onClick={onShortsClick}
            />
          )}
        </div>
      )}
    </div>
  )
}
