import classnames from 'classnames'
import { useAtomValue } from 'jotai'
import { screenOrientationAtom } from '@/stores'
import { SvgIcon } from '@/components/ui/SvgIcon'
import { ToolbarItem } from '../hooks/useToolbar'
import styles from '../Detail.module.css'

export interface ImageToolbarProps {
  toolbarList: ToolbarItem[]
  onItemClick: (op: string) => void
}

/**
 * 图片工具栏组件
 * 负责渲染工具栏按钮并处理点击事件
 */
export const ImageToolbar: React.FC<ImageToolbarProps> = ({
  toolbarList,
  onItemClick,
}) => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  
  return (
    <ul
      className={classnames([
        styles.toolbar,
        {
          [styles.toolbarHorizontal]: screenOrientation.isLandScape,
        },
      ])}
    >
      {toolbarList.map((item, index) => (
        <li
          className={classnames([item.disabled ? 'opacity-50' : ''])}
          key={index}
          onClick={() => onItemClick(item.disabled ? '' : item.op)}
        >
          <SvgIcon
            src={item.icon}
            alt={item.title}
            svgClassName="w-14 h-14"
          />
          <p className="text-[2rem] text-white text-center font-semibold leading-[138%] absolute -left-[20%] -right-[20%] top-[118%]">
            {item.title}
          </p>
        </li>
      ))}
    </ul>
  )
}
