import { SendToEmailModal } from '../SendToEmailModal'
import { DownloadModal } from '../DownloadModal'
import { ShareModal } from '../ShareModal'
import { PrintModal } from '../PrintModal'
import { ScanQrPrintModal } from '../ScanQrPrintModal'
import { GenerateShortsModal } from '../GenerateShortsModal'
import { SignatureModal } from '../SignatureModal'
import { UseModalsReturn } from '../hooks/useModals'
import { SignatureData } from '../hooks/useSignature'
import { ShortsData } from '../hooks/useVideoGeneration'
import { MyMirrorAiTask } from '@/stores/types'
import { VirtualInfo } from '@/apis/types'

export interface ImageModalsProps {
  // 模态框控制
  modals: UseModalsReturn

  // 数据
  curMaterialId: { material_id: number; image_id: number }[] | null
  curImg: MyMirrorAiTask | null
  curMazeMaterialImageUrl?: string
  curMaterialDetail?: any
  curVirtualInfo?: VirtualInfo
  curVideo?: any
  shorts: ShortsData | null

  // 回调函数
  onSignatureComplete: (data: SignatureData) => Promise<void>
}

/**
 * 统一管理所有模态框的组件
 * 负责渲染所有模态框并传递相应的props
 */
export const ImageModals: React.FC<ImageModalsProps> = ({
  modals,
  curMaterialId,
  curImg,
  curMazeMaterialImageUrl,
  curMaterialDetail,
  curVirtualInfo,
  curVideo,
  shorts,
  onSignatureComplete,
}) => {
  const { isModalOpen, closeModal } = modals

  // 构建下载URL
  const getDownloadUrl = () => {
    if (
      curVirtualInfo?.status === 1 &&
      curVirtualInfo?.expire_ts * 1000 > +new Date()
    ) {
      return `${window.location.href}&virtual_uid=${curVirtualInfo?.uid}&navigate=noop`
    }
    return curVideo ? curVideo.resultUrl : curMazeMaterialImageUrl
  }

  return (
    <>
      {/* 邮件模态框 */}
      <SendToEmailModal
        open={isModalOpen('email')}
        setOpen={closeModal}
        imgId={curMaterialId}
      />

      {/* 下载模态框 */}
      <DownloadModal
        open={isModalOpen('download')}
        setOpen={closeModal}
        mazeImgUrl={getDownloadUrl()}
      />

      {/* 下载视频模态框 */}
      <DownloadModal
        open={isModalOpen('downloadShorts')}
        setOpen={closeModal}
        tipsText={'扫描二维码下载视频'}
        mazeImgUrl={shorts?.url || shorts?.origin_url || ''}
      />

      {/* 打印模态框 */}
      <PrintModal
        open={isModalOpen('print')}
        setOpen={closeModal}
        curImg={curImg}
        mazeImgUrl={curMazeMaterialImageUrl}
      />

      {/* 分享模态框 */}
      <ShareModal
        open={isModalOpen('share')}
        setOpen={closeModal}
        mazeImgUrl={curMazeMaterialImageUrl}
      />

      {/* 扫码打印模态框 */}
      <ScanQrPrintModal
        open={isModalOpen('scanQrPrint')}
        setOpen={closeModal}
        mazeImgUrl={curMazeMaterialImageUrl}
        curMaterialDetail={curMaterialDetail}
      />

      {/* 视频生成模态框 */}
      <GenerateShortsModal
        open={isModalOpen('shorts')}
        setOpen={closeModal}
        shorts={shorts}
      />

      {/* 签名模态框 */}
      <SignatureModal
        open={isModalOpen('signature')}
        setOpen={closeModal}
        onSignatureComplete={onSignatureComplete}
      />
    </>
  )
}
