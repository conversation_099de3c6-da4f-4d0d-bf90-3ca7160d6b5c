import classNames from 'classnames'
import { useAtomValue } from 'jotai'
import { useTranslation } from 'react-i18next'
import { screenOrientationAtom, selectedEventDetailAtom } from '@/stores'
import { MirrorLoading } from 'wujieai-react-icon'
import { LikeOrNot } from './LikeOrNot'

// 导入自定义hooks
import { useImageData } from './hooks/useImageData'
import { useModals } from './hooks/useModals'
import { useToolbar } from './hooks/useToolbar'
import { useSignature } from './hooks/useSignature'
import { useVideoGeneration } from './hooks/useVideoGeneration'
import { useImageEffects } from './hooks/useImageEffects'

// 导入子组件
import { ImageSlideshow } from './components/ImageSlideshow'
import { ImageToolbar } from './components/ImageToolbar'
import { ImageModals } from './components/ImageModals'

export const ImageDetail = () => {
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const selectedEventDetail = useAtomValue(selectedEventDetailAtom)

  // 使用自定义hooks管理各种逻辑
  const imageData = useImageData()
  const modals = useModals()
  const signature = useSignature({
    curMaterialDetail: imageData.curMaterialDetail,
    mutateMateriaDetail: imageData.mutateMateriaDetail,
  })
  const videoGeneration = useVideoGeneration({
    selectedImageData: imageData.selectedImageData,
    activeImageIndex: imageData.activeImageIndex,
  })
  const effects = useImageEffects({
    curImg: imageData.curImg,
  })

  // 工具栏逻辑
  const toolbar = useToolbar(
    {
      curMazeMaterialImageUrl: imageData.curMazeMaterialImageUrl,
      curMaterialId: imageData.curMaterialId,
      curVideo: imageData.curVideo,
      shorts: videoGeneration.shorts,
      generateShortsPending: videoGeneration.generateShortsPending,
      onGenerateShorts: videoGeneration.handleGenerateShorts,
    },
    modals.openModal
  )

  return (
    <>
      <div
        className={classNames(
          'flex items-center justify-center gap-20 h-full w-full flex-col pt-12',
          {
            'pt-6 !gap-10 !justify-start': screenOrientation.isLandScape,
          }
        )}
      >
        {imageData.selectedImageData.images.length > 0 ? (
          <>
            {/* 图片轮播展示 */}
            <ImageSlideshow
              imageTemplates={imageData.imageTemplates}
              activeImageIndex={imageData.activeImageIndex}
              curImg={imageData.curImg}
              setActiveTemplate={imageData.setActiveTemplate}
              signatureData={signature.signatureData}
              shorts={videoGeneration.shorts}
              generateShortsPending={videoGeneration.generateShortsPending}
              onShortsClick={() => modals.openModal('shorts')}
            />

            {/* 标题 */}
            <h2 className="maze-primary-text text-[2.5rem] text-center opacity-0 animate-text-fade-in phone:py-4">
              {t('分享你的AI写真')}
            </h2>

            {/* 工具栏 */}
            <ImageToolbar
              toolbarList={toolbar.toolbarList}
              onItemClick={toolbar.handleToolbarClick}
            />

            {/* 点赞按钮 */}
            {effects.isLikeBtnShow &&
              selectedEventDetail?.enable_evaluation &&
              imageData.curImg && (
                <div
                  className={classNames(
                    'absolute w-[50%] bottom-36 z-10 right-0',
                    screenOrientation.isLandScape
                      ? 'scale-75 bottom-[6%] right-[30%]'
                      : 'right-[22%] ipad:bottom-28 ipad:right-[22%] ipad:scale-75'
                  )}
                >
                  <LikeOrNot
                    imgId={imageData.curMaterialDetail?.id}
                    after={() => effects.setIsLikeBtnShow(false)}
                  />
                </div>
              )}
          </>
        ) : (
          /* 加载状态 */
          <div className="flex items-center justify-center h-full">
            <div className="relative w-[6rem] h-[6rem] opacity-30">
              <MirrorLoading className="w-full h-full absolute left-0 top-0 animate-spin maze-primary-text" />
            </div>
          </div>
        )}

        {/* 动效画布 */}
        <canvas
          className="fixed top-0 left-0 right-0 bottom-0 w-full h-full -z-10"
          id="detail-canvas-container"
        />
      </div>

      {/* 所有模态框 */}
      <ImageModals
        modals={modals}
        curMaterialId={imageData.curMaterialId}
        curImg={imageData.curImg}
        curMazeMaterialImageUrl={imageData.curMazeMaterialImageUrl}
        curMaterialDetail={imageData.curMaterialDetail}
        curVirtualInfo={effects.curVirtualInfo}
        curVideo={imageData.curVideo}
        shorts={videoGeneration.shorts}
        onSignatureComplete={signature.handleSignatureComplete}
      />
    </>
  )
}
