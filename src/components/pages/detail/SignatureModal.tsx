import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { useRef, useState } from 'react'
import { SvgIcon } from '@/components/ui/SvgIcon'
import { publicPreLoadSourceObj } from '@/configs/source'
import classNames from 'classnames'
import { useAtomValue } from 'jotai'
import { screenOrientationAtom } from '@/stores'

import {
  ReactSketchCanvas,
  type ReactSketchCanvasRef,
} from 'react-sketch-canvas'
import { SketchPicker, ColorResult } from 'react-color'

interface SignatureModalProps {
  open: boolean
  setOpen: (open: boolean) => void
  onSignatureComplete?: (signatureData: {
    imageData: string
    position: 'left-bottom' | 'right-bottom'
    size: 'small' | 'large'
  }) => void
}

export const SignatureModal: React.FC<SignatureModalProps> = ({
  open,
  setOpen,
  onSignatureComplete,
}) => {
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const canvasRef = useRef<ReactSketchCanvasRef>(null)

  // 签名工具状态
  const [strokeColor, setStrokeColor] = useState('#000000')
  const [signaturePosition, setSignaturePosition] = useState<
    'left-bottom' | 'right-bottom'
  >('right-bottom')
  const [signatureSize, setSignatureSize] = useState<'small' | 'large'>('small')
  const [showColorPicker, setShowColorPicker] = useState(false)

  const handleColorChange = (color: ColorResult) => {
    setStrokeColor(color.hex)
  }

  const toggleColorPicker = () => {
    setShowColorPicker(!showColorPicker)
  }

  const handleUndo = () => {
    canvasRef.current?.undo()
  }

  const handleClear = () => {
    canvasRef.current?.clearCanvas()
  }

  const handleSizeChange = (size: 'small' | 'large') => {
    setSignatureSize(size)
  }

  const handlePositionChange = (position: 'left-bottom' | 'right-bottom') => {
    setSignaturePosition(position)
  }

  const handleConfirm = async () => {
    try {
      if (canvasRef.current) {
        const imageData = await canvasRef.current.exportImage('png')
        onSignatureComplete?.({
          imageData,
          position: signaturePosition,
          size: signatureSize,
        })
        setOpen(false)
      }
    } catch (error) {
      console.error('导出签名失败:', error)
    }
  }

  const handleClose = () => {
    setOpen(false)
  }

  return (
    <MyModal
      open={open}
      width={screenOrientation.isLandScape ? 800 : '80vw'}
      className="p-0 border-none pb-12"
      contentClassName="!p-0 border-none"
      content={
        <div className="flex flex-col h-full w-full px-12">
          {/* 标题 */}
          <div className="text-center pt-12">
            <h2 className="text-[2rem] font-semibold maze-primary-text">
              {t('请在下方区域签名')}
            </h2>
          </div>

          {/* 签名区域 */}
          <div className="flex-1 py-12 flex justify-center items-center">
            <div className="w-full h-[20vh] min-h-[220px] rounded-lg">
              <ReactSketchCanvas
                ref={canvasRef}
                strokeColor={strokeColor}
                strokeWidth={12}
                canvasColor="transparent"
                width="100%"
                height="100%"
                style={{
                  borderRadius: '16px',
                  background: '#444B5F',
                }}
              />
            </div>
          </div>

          {/* 工具栏 */}
          <div className="pb-10">
            <div className="w-full flex justify-center items-center gap-3">
              {/* 颜色选择 */}
              <div
                onClick={toggleColorPicker}
                className="flex items-center flex-col justify-center gap-4 relative"
              >
                <SvgIcon
                  style={{ color: strokeColor }}
                  src={publicPreLoadSourceObj.detailColor}
                  svgClassName="w-10 h-10"
                />
                <span className="text-[1.5rem]">{t('颜色')}</span>
                {showColorPicker && (
                  <div className="absolute top-12 left-0 z-50">
                    <div
                      className="fixed inset-0"
                      onClick={() => setShowColorPicker(false)}
                    />
                    <SketchPicker
                      width={'300px'}
                      color={strokeColor}
                      onChange={handleColorChange}
                      disableAlpha
                    />
                  </div>
                )}
              </div>

              {/* 撤销 */}
              <button
                className="flex items-center flex-col justify-center gap-3 px-3 py-2 rounded-lg"
                onClick={handleUndo}
                title={t('撤销')}
              >
                <SvgIcon
                  src={publicPreLoadSourceObj.detailRedo}
                  svgClassName="w-10 h-10"
                />
                <span className="text-[1.5rem]">{t('撤销')}</span>
              </button>

              {/* 清空 */}
              <button
                className="flex items-center flex-col justify-center gap-3 px-3 py-2 rounded-lg"
                onClick={handleClear}
                title={t('清空')}
              >
                <SvgIcon
                  src={publicPreLoadSourceObj.detailClear}
                  svgClassName="w-10 h-10"
                />
                <span className="text-[1.5rem]">{t('清空')}</span>
              </button>
              <span className="border-r-[1px] mx-6 h-[3.6rem] border-white inline-block"></span>
              {/* 位置选择 */}
              <button
                className={classNames(
                  'flex items-center flex-col justify-center gap-3 px-3 py-2 rounded-lg',
                  signaturePosition === 'left-bottom'
                    ? 'text-[#BE8EFF]'
                    : 'text-white'
                )}
                onClick={() => handlePositionChange('left-bottom')}
              >
                <SvgIcon
                  src={publicPreLoadSourceObj.detailPositionBottomLeft}
                  svgClassName="w-10 h-10"
                />
                <span className="text-[1.5rem]">{t('左下')}</span>
              </button>
              <button
                className={classNames(
                  'flex items-center flex-col justify-center gap-3 px-3 py-2 rounded-lg',
                  signaturePosition === 'right-bottom'
                    ? 'text-[#BE8EFF]'
                    : 'text-white'
                )}
                onClick={() => handlePositionChange('right-bottom')}
              >
                <SvgIcon
                  src={publicPreLoadSourceObj.detailPositionBottomRight}
                  svgClassName="w-10 h-10"
                />
                <span className="text-[1.5rem]">{t('右下')}</span>
              </button>
              {/* 字体大小 */}
              <button
                className={classNames(
                  'flex items-center flex-col justify-center gap-3 px-3 py-2 rounded-lg',
                  signatureSize === 'small' ? 'text-[#BE8EFF]' : 'text-white'
                )}
                onClick={() => handleSizeChange('small')}
                title={t('小字体')}
              >
                <SvgIcon
                  src={publicPreLoadSourceObj.detailFontSmall}
                  svgClassName="w-10 h-10"
                />
                <span className="text-[1.5rem]">{t('小字体')}</span>
              </button>
              <button
                className={classNames(
                  'flex items-center flex-col justify-center gap-3 px-3 py-2 rounded-lg',
                  signatureSize === 'large' ? 'text-[#BE8EFF]' : 'text-white'
                )}
                onClick={() => handleSizeChange('large')}
                title={t('大字体')}
              >
                <SvgIcon
                  src={publicPreLoadSourceObj.detailFontLarge}
                  svgClassName="w-10 h-10"
                />
                <span className="text-[1.5rem]">{t('大字体')}</span>
              </button>
            </div>
          </div>
        </div>
      }
      onOk={handleConfirm}
      okText={t('确定')}
      showOkButton={true}
      showCancelButton={false}
      onCancel={handleClose}
    />
  )
}
