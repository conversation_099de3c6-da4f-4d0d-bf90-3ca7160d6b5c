.toolbar {
  @apply flex justify-center items-center gap-10 ipad:gap-12;
  li {
    @apply relative border-2 rounded-full p-7 cursor-pointer w-36 h-36 flex justify-center items-center phone:border-[1px] ipad:w-[7rem] ipad:h-[7rem];
  }
}
.toolbarHorizontal {
  li {
    @apply w-32 h-32;
  }
}
/* 竖 */
.detailItem {
  @apply w-auto h-auto max-w-[72%] ipad:max-w-[56%];
}
/* 横 */
.detailItemHorizontal {
  @apply w-auto h-auto max-w-[22%];
}
