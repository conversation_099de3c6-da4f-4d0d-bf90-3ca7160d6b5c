.tabs {
  @apply flex space-x-16 justify-center items-center;
}
.tab {
  @apply w-32 h-32 rounded-full opacity-40 border-2 flex justify-center items-center leading-none cursor-pointer transform transition-all phone:border-[1px];
  &.male {
    background: linear-gradient(
      180deg,
      rgba(93, 126, 255, 0.8) 0%,
      rgba(255, 213, 213, 0.8) 100%
    );
  }
  &.female {
    background: linear-gradient(
      180deg,
      rgba(255, 119, 119, 0.8) 0%,
      rgba(240, 221, 255, 0.8) 100%
    );
  }
}
.tabActive {
  @apply btn-text-color maze-bg-gradient-primary opacity-100 border-[0.375rem];
  box-shadow: 0px 0px 20px 0px #fff;
}
