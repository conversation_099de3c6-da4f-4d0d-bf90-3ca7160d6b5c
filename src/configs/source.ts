import { toCDNImage } from '@/utils'

/** public下需要预加载的资源 */
export const publicPreLoadSourceObj = {
  loadingVideo: toCDNImage('/images/video/loading-print.mp4'),
  loadingVideoPoster: toCDNImage('/images/video/loading-print.jpg'),
  blur: toCDNImage('/images/common/blur.png'),
  correctImage: toCDNImage('/images/photo/correctImage.png'),
  errorImage: toCDNImage('/images/photo/errorImage.png'),
  correctMultipleImage: toCDNImage('/images/photo/correctImage-multiple-2.png'),
  errorMultipleImage: toCDNImage('/images/photo/errorImage-multiple-2.png'),
  enterPicture: toCDNImage('/images/homepage/inter-picture.jpg'),
  enterVideo: toCDNImage('/images/homepage/inter-video-2.mp4'),
  enterVideoPoster: toCDNImage('/images/homepage/inter-video-poster.jpg'),
  silentVerticalBgImg: toCDNImage('/images/silent/maze_silent_vertical.webp'),
  silentCrosswiseBgImg: toCDNImage('/images/silent/maze_silent_crosswise.webp'),
  guideMaleYes: toCDNImage('/images/photo/guide-male-yes.svg'),
  guideMaleNo: toCDNImage('/images/photo/guide-male-no.svg'),
  guideFemaleYes: toCDNImage('/images/photo/guide-female-yes.svg'),
  guideFemaleNo: toCDNImage('/images/photo/guide-female-no.svg'),
  guideChildYes: toCDNImage('/images/photo/guide-child-yes.svg'),
  guideChildNo: toCDNImage('/images/photo/guide-child-no.svg'),
  guideCoupleYes: toCDNImage('/images/photo/guide-couple-yes.svg'),
  guideCoupleNo: toCDNImage('/images/photo/guide-couple-no.svg'),
  guideLight: toCDNImage('/images/photo/guide-light.svg'),
  shootRetry: toCDNImage('/images/icons/retry.svg'),
  shootConfirm: toCDNImage('/images/icons/confirm.svg'),
  close: toCDNImage('/images/icons/close.svg'),
  detailPrint: toCDNImage('/images/icons/detail/print.svg'),
  detailMsg: toCDNImage('/images/icons/detail/msg.svg'),
  detailDownload: toCDNImage('/images/icons/detail/download.svg'),
  detailShare: toCDNImage('/images/icons/detail/share.svg'),
  detailShorts: toCDNImage('/images/icons/detail/shorts.svg'),
  detailSign: toCDNImage('/images/icons/detail/sign.svg'),
  detailEmail: toCDNImage('/images/icons/detail/email.svg'),
  detailRedo: toCDNImage('/images/icons/detail/redo.svg'),
  detailFontSmall: toCDNImage('/images/icons/detail/font-small.svg'),
  detailFontLarge: toCDNImage('/images/icons/detail/font-large.svg'),
  detailClear: toCDNImage('/images/icons/detail/clear.svg'),
  detailPositionBottomLeft: toCDNImage('/images/icons/detail/left-bottom.svg'),
  detailPositionBottomRight: toCDNImage(
    '/images/icons/detail/right-bottom.svg'
  ),
  detailColor: toCDNImage('/images/icons/detail/color.svg'),
  like: toCDNImage('/images/icons/detail/like2.svg'),
  dislike: toCDNImage('/images/icons/detail/dislike.svg'),
  photoMask: toCDNImage('/images/photo/mask.png'),
  photoMaskV: toCDNImage('/images/photo/mask-v.png'),
  photoMaskCouple: toCDNImage('/images/photo/mask-couple.png'),
  photoMaskCoupleV: toCDNImage('/images/photo/mask-couple-v.png'),
  photoMaskIpad: toCDNImage('/images/photo/mask-couple-ipad.png'),
  photoMaskPhone: toCDNImage('/images/photo/mask-phone.png'),
  iconCorrect: toCDNImage('/images/photo/correct.svg'),
  iconError: toCDNImage('/images/photo/error.svg'),
  female: toCDNImage('/images/icons/female.png'),
  male: toCDNImage('/images/icons/male.png'),
  painting: toCDNImage('/images/common/painting.png'),
  play: toCDNImage('/images/icons/video.svg'),
  zoom: toCDNImage('/images/icons/zoom.svg'),
  frame: toCDNImage('/images/photo/frame.png'),
  payMethod: toCDNImage('/images/result/pay-method.png'),
  paySuccess: toCDNImage('/images/icons/success.png'),
  payFail: toCDNImage('/images/icons/fail.png'),
}

export const publicPreLoadVideos = [
  publicPreLoadSourceObj.loadingVideo,
  publicPreLoadSourceObj.enterVideo,
  publicPreLoadSourceObj.enterVideoPoster,
]

export const publicPreLoadImages = [
  publicPreLoadSourceObj.loadingVideoPoster,
  publicPreLoadSourceObj.silentVerticalBgImg,
  publicPreLoadSourceObj.silentCrosswiseBgImg,
  publicPreLoadSourceObj.guideLight,
  publicPreLoadSourceObj.shootRetry,
  publicPreLoadSourceObj.shootConfirm,
  publicPreLoadSourceObj.detailPrint,
  publicPreLoadSourceObj.detailMsg,
  publicPreLoadSourceObj.detailDownload,
  publicPreLoadSourceObj.detailShare,
  publicPreLoadSourceObj.detailEmail,
  publicPreLoadSourceObj.like,
  publicPreLoadSourceObj.dislike,
  publicPreLoadSourceObj.photoMask,
  publicPreLoadSourceObj.photoMaskV,
  publicPreLoadSourceObj.iconCorrect,
  publicPreLoadSourceObj.iconError,
  publicPreLoadSourceObj.female,
  publicPreLoadSourceObj.male,
  publicPreLoadSourceObj.painting,
  publicPreLoadSourceObj.play,
  publicPreLoadSourceObj.zoom,
  publicPreLoadSourceObj.photoMaskPhone,
  publicPreLoadSourceObj.frame,
  publicPreLoadSourceObj.payMethod,
  publicPreLoadSourceObj.paySuccess,
  publicPreLoadSourceObj.payFail,
  publicPreLoadSourceObj.close,
  publicPreLoadSourceObj.guideMaleYes,
  publicPreLoadSourceObj.guideMaleNo,
  publicPreLoadSourceObj.guideFemaleYes,
  publicPreLoadSourceObj.guideFemaleNo,
  publicPreLoadSourceObj.guideChildYes,
  publicPreLoadSourceObj.guideChildNo,
  publicPreLoadSourceObj.guideCoupleYes,
  publicPreLoadSourceObj.guideCoupleNo,
  publicPreLoadSourceObj.photoMaskCouple,
  publicPreLoadSourceObj.photoMaskCoupleV,
  publicPreLoadSourceObj.photoMaskIpad,
  publicPreLoadSourceObj.detailShorts,
  publicPreLoadSourceObj.detailSign,
  publicPreLoadSourceObj.detailRedo,
  publicPreLoadSourceObj.detailFontSmall,
  publicPreLoadSourceObj.detailFontLarge,
  publicPreLoadSourceObj.detailClear,
  publicPreLoadSourceObj.detailColor,
]
