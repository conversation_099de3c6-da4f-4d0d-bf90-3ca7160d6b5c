import { atom } from 'jotai'
import {
  CameraStatus,
  CameraTypeEnum,
  MachineStatus,
  MyMirrorAiTask,
  NetworkStatus,
  PrinterStatus,
  ScreenOrientation,
  ThemeConfigProps,
} from './types'
import {
  AiTaskType,
  CategoryListFragment,
  DeviceType,
  DrawOrderPayInfoFragment,
  PrintOrderPayInfoFragment,
  MultiScreenDisplayEnum,
  MirrorAiTaskDetailOrderFragment,
  MirrorSexEnum,
} from '@/graphqls/types'
import { atomWithReset, atomWithStorage } from 'jotai/utils'
import { EventItem, FrameDetail, ThemeDetail, ThemeType } from '@/apis/types'

/** 机器信息 */
export const machineInfoAtom = atom<MachineStatus>({
  /** 唯一标识 */
  uuid: null,
  /** 屏幕宽 */
  screenWidth: 0,
  /** 屏幕高 */
  screenHeight: 0,
  /** 摄像头状态，默认无模块 */
  cameraStatus: CameraStatus.NONE,
  /** 摄像头类型 */
  cameraType: CameraTypeEnum.NORMAL,
  /** 打印机状态，默认无模块 */
  printerStatus: PrinterStatus.NONE,
  /** 打印机打印纸剩余*/
  printerPaperNum: 10,
  /** 打印机墨水状态 */
  printerInkStatus: PrinterStatus.INK_PLENTY,
  /** 网络状态 */
  networkStatus: NetworkStatus.ONLINE,
  printers: null,
  hasSecondaryScreen: false,
})

export const themeConfigAtom = atom<ThemeConfigProps | undefined>(undefined)

/** 资源包的hash */
export const resourceHashAtom = atom<string | undefined>(undefined)

/** 拍照次数 */
export const shotNumAtom = atom<number>(0)

/** 每次作画张数 */
export const imageNumAtom = atom<number>(1)

/** 是否启用摄像头-后台配置获取 */
export const cameraEnableAtom = atom<boolean>(false)

/** 采用竖屏摄像头(默认横屏摄像头）-后台配置获取 */
export const isPortraitCameraEnableAtom = atom<boolean>(false)

/** 是否启用打印机-后台配置获取 */
export const printerEnableAtom = atom<boolean>(false)

/** 是否为分体式设备 */
export const isSplitPrintingAtom = atom<boolean>(false)

/** 设备负责人电话 */
export const phoneAtom = atom<string | undefined | null>(null)

/** 多屏同显/异显 */
export const multiScreenDisplayAtom = atom<MultiScreenDisplayEnum>(
  MultiScreenDisplayEnum.SAME
)

/** 是否是官方商户 */
export const isOfficialMerchantAtom = atom<boolean>(false)

/** 机器设备类型：1.标准版 2.免费版 */
export const deviceTypeAtom = atom<DeviceType>(DeviceType.MIRROR)

/** 是否是标准版（收费版） */
export const isPaidVersionAtom = atom<boolean>(get => {
  return get(deviceTypeAtom) === DeviceType.MIRROR
})

/** 是否实际支付 */
export const isRealPayAtom = atom<boolean>(false)

/**是否支持视频 */
export const isSupportVideoAtom = atom<boolean>(false)

/** 图片模板列表 */
export const resourceTemplateAtom = atom<ThemeDetail[]>([])

/** 视频模板列表 */
export const resourceVideoTemplateAtom = atom<CategoryListFragment[]>([])

/** 静默页配置 */
export const silentConfigAtom = atom<{
  images: string[]
  portraitImages: string[] // 竖版静默页图片
  timer: number
}>({
  images: [],
  portraitImages: [],
  timer: 0,
})

/** 打印价格信息 */
export const printPriceAtom = atom<{
  /** 6寸打印价格 */
  sixPrice: number
  /** 4寸打印价格 */
  a4Price: number
}>({
  sixPrice: 0,
  a4Price: 0,
})

/** 是否使用人群筛选 */
export const isUseCrowdAtom = atom<boolean>(false)

/** 用户操作行为的一些atom，使用atomWithReset */

/** 是否选择过作画类型 */
export const isTaskTypeSelectedAtom = atomWithReset<boolean>(false)

/** 作画类型 */
export const taskTypeAtom = atomWithReset<AiTaskType>(AiTaskType.DRAW)

/** 作品结果图 - 使用map结构存储不同taskBaseId的结果 */
export const resultImagesAtom = atomWithReset<MyMirrorAiTask[]>([])

export const mazeResultImagesAtom = atomWithReset<
  Record<number, MyMirrorAiTask[]>
>({})

/** 作品结果订单信息 */
export const resultOrderAtom = atomWithReset<MirrorAiTaskDetailOrderFragment>(
  {}
)

/** 默认打印的作品index */
export const defaultPrintIndexAtom = atomWithReset<number>(0)

/** 用户上传的图片 */
export const userUploadImageAtom = atomWithReset<string | undefined>(undefined)

/** 作画支付订单信息 */
export const drawPayOrderAtom = atomWithReset<DrawOrderPayInfoFragment>({})
// maze 订单，多个构成
export const mazeDrawPayOrderAtom = atomWithReset<DrawOrderPayInfoFragment[]>(
  []
)

/** 打印支付订单信息 */
export const printPayOrderAtom = atomWithReset<PrintOrderPayInfoFragment>({})

/** 全局缩放比例 */
export const scaleAtom = atom(1)
/** 屏幕方向：横屏/竖屏 */
export const screenOrientationAtom = atom<ScreenOrientation>({
  isLandScape: true,
  isPortrait: false,
})
/** 领取电子相册二维码 */
export const photoAlbumQrcodeAtom = atom<string | null | undefined>(null)

/** 能否展示贴片视频 */
export const canShowVideoAtom = atom<boolean>(false)

// 用户选择的活动 ID
export const selectedEventIdAtom = atomWithStorage<number | undefined>(
  'selectedEventIdAtom',
  undefined
)
// 用户选择的活动详情
export const selectedEventDetailAtom = atomWithStorage<EventItem | undefined>(
  'selectedEventDetail',
  undefined
)

// 用户选择的边框 ID
export const selectedImageFrameAtom = atomWithStorage<FrameDetail | undefined>(
  'selectedImageFrameAtom',
  undefined
)

// 是否展示主题详情弹窗
export const isShowThemeDetailModalAtom = atom<boolean>(false)

// 当前选择的主题详情
export const selectedThemeDetailAtom = atomWithStorage<ThemeDetail | undefined>(
  'selectedThemeDetailAtom',
  undefined
)

// 当前主题单次做图数量
// 废弃，后端处理
export const modelsPerGenerationCountAtom = atomWithStorage<number | undefined>(
  'modelsPerGenerationCountAtom',
  4
)

// 当前主题单个模型做图数量
// 废弃，后端处理作画
export const generationsPerModelCountAtom = atomWithStorage<number | undefined>(
  'generationsPerModelCountAtom',
  1
)

// 当前选择的性别
export const selectedGenderAtom = atomWithStorage<string>(
  'selectedGenderAtom',
  MirrorSexEnum.FEMALE
)

/** 全局视频播放管理 */
export const currentPlayingVideoIdAtom = atom<string | null>(null)

/** 媒体类型枚举 */
export enum MediaType {
  VIDEO = 'video',
  IMAGE = 'image',
}

/** 全局媒体播放器弹窗状态管理 */
export const globalVideoPlayerAtom = atom<{
  open: boolean
  mediaType?: MediaType
  video?: MyMirrorAiTask
  imageUrl?: string | null | undefined
  poster?: string | null | undefined
}>({
  open: false,
  mediaType: undefined,
  video: undefined,
  imageUrl: undefined,
  poster: undefined,
})
/** 当前模板类型，male female couple child */
export const currentThemeTypeAtom = atom<ThemeType | undefined>(undefined)
