/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx,css}',
    './index.html',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
        md: '768px',
      },
    },
    extend: {
      fontSize: {
        xs: ['0.75rem', '1.25rem'],
        sm: ['0.875rem', '1.5rem'],
        base: ['1rem', '1.5rem'],
        lg: ['1.5rem', '2rem'],
        xl: ['2rem', '2.5rem'],
        '2xl': ['2.5rem', '3rem'],
      },
      colors: {
        primary: 'rgb(var(--primary-raw) / <alpha-value>)',
        error: 'rgb(var(--error-raw) / <alpha-value>)',
        success: 'rgb(var(--success-raw) / <alpha-value>)',
        'liner-blue': 'rgb(var(--liner-blue-raw) / <alpha-value>)',
        'liner-pink': 'rgb(var(--liner-pink-raw) / <alpha-value>)',
        'neutral-50': 'rgb(var(--neutral-50-raw) / <alpha-value>)',
        'neutral-100': 'rgb(var(--neutral-100-raw) / <alpha-value>)', // d1
        'neutral-200': 'rgb(var(--neutral-200-raw) / <alpha-value>)', // d2
        'neutral-300': 'rgb(var(--neutral-300-raw) / <alpha-value>)', // d3
        'neutral-400': 'rgb(var(--neutral-400-raw) / <alpha-value>)', // d4
        'neutral-500': 'rgb(var(--neutral-500-raw) / <alpha-value>)', // d5
        'neutral-600': 'rgb(var(--neutral-600-raw) / <alpha-value>)', // light-1
        'neutral-700': 'rgb(var(--neutral-700-raw) / <alpha-value>)', // light-2
        'neutral-800': 'rgb(var(--neutral-800-raw) / <alpha-value>)', // light-3
        'neutral-900': 'rgb(var(--neutral-900-raw) / <alpha-value>)',

        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        shadPrimary: {
          DEFAULT: 'hsl(var(--shad-primary))',
          foreground: 'hsl(var(--shad-primary-foreground))',
        },
        shadSecondary: {
          DEFAULT: 'hsl(var(--shad-secondary))',
          foreground: 'hsl(var(--shad-secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'calc(var(--radius) + 8px)',
        md: 'var(--radius)',
        sm: 'calc(var(--radius) - 4px)',
      },
      borderWidth: {
        3: '3px',
      },
      borderColor: {
        'light-primary': 'rgba(139, 92, 246, 0.16)',
      },
      keyframes: {
        'opacity-in': {
          from: { opacity: 0 },
          to: { opacity: 1 },
        },
        'opacity-out': {
          from: { opacity: 1 },
          to: { opacity: 0 },
        },
        'text-fade-in': {
          '0%': {
            transform: 'scale(0.5)',
            opacity: 0,
          },
          '40%': {
            transform: 'scale(1.32)',
            opacity: 0.8,
          },
          '100%': {
            transform: 'scale(1)',
            opacity: 1,
          },
        },
        'float-in': {
          '0%': {
            transform: 'translateY(0px)',
          },
          '50%': {
            transform: 'translateY(-6px)',
          },
          '100%': {
            transform: 'translateY(0px)',
          },
        },
        'button-float': {
          '0%': {
            transform: 'translateY(0px)',
          },
          '50%': {
            transform: 'translateY(-20px)',
          },
          '100%': {
            transform: 'translateY(0px)',
          },
        },
        'breathing-glow': {
          '0%': {
            boxShadow:
              '0 0 20px rgba(214, 92, 225, 0.3), 0 0 40px rgba(128, 114, 237, 0.2), 0 0 60px rgba(214, 92, 225, 0.1)',
          },
          '50%': {
            boxShadow:
              '0 0 80px rgba(214, 92, 225, 0.8), 0 0 120px rgba(128, 114, 237, 0.6), 0 0 200px rgba(214, 92, 225, 0.6)',
          },
          '100%': {
            boxShadow:
              '0 0 20px rgba(214, 92, 225, 0.3), 0 0 40px rgba(128, 114, 237, 0.2), 0 0 60px rgba(214, 92, 225, 0.1)',
          },
        },
      },
      animation: {
        'opacity-in': 'opacity-in 0.2s ',
        'opacity-out': 'opacity-out 0.2s ',
        'text-fade-in': 'text-fade-in 0.36s ease-in-out 3s forwards',
        'float-in': 'float-in 3s ease-in-out infinite',
        'button-float': 'button-float 6s ease-in-out infinite',
        'breathing-glow': 'breathing-glow 6s ease-in-out infinite',
      },
      screens: {
        ipad: { min: '768px', max: '1024px' }, // 适配的是竖屏模式下的ipad，不考虑横屏的尺寸
        phone: { min: '320px', max: '475px' }, // 适配的是竖屏模式下的iphone，不考虑横屏的尺寸
        iphone: { min: '320px', max: '475px' }, // 适配的是竖屏模式下的iphone，不考虑横屏的尺寸
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    // 自定义工具类插件 - 解决 CSS Modules 中 @apply 无法识别自定义类的问题
    function ({ addUtilities }) {
      const customUtilities = {
        // 阴影相关
        '.shadow-button-primary': {
          'box-shadow':
            '0px 76px 52px rgba(139, 92, 246, 0.2), 0px 20px 28px rgba(139, 92, 246, 0.36)',
        },
        '.shadow-button-neutral': {
          'box-shadow':
            '0px 76px 52px rgba(9, 9, 9, 0.06), 0px 20px 28px rgba(0, 0, 0, 0.12)',
        },
        '.shadow-box-neutral': {
          'box-shadow': '0px 48px 64px rgba(0, 0, 0, 0.16)',
        },
        '.shadow-box-primary': {
          'box-shadow':
            '0px 11px 25px rgba(112, 65, 200, 0.1), 0px 45px 45px rgba(112, 65, 200, 0.09), 0px 102px 61px rgba(112, 65, 200, 0.05), 0px 181px 72px rgba(112, 65, 200, 0.01), 0px 282px 79px rgba(112, 65, 200, 0)',
        },
        '.shadow-box-small-primary': {
          'box-shadow':
            '0px 8px 18px rgba(112, 65, 200, 0.1), 0px 32px 32px rgba(112, 65, 200, 0.09), 0px 72px 44px rgba(112, 65, 200, 0.05), 0px 130px 51px rgba(112, 65, 200, 0.01), 0px 200px 56px rgba(112, 65, 200, 0)',
        },

        // 渐变相关
        '.text-gradient-primary': {
          background: 'linear-gradient(321deg, #6964de -10.33%, #fca6e9 100%)',
          '-webkit-background-clip': 'text',
          color: 'transparent',
        },
        '.bg-gradient-primary': {
          background: 'linear-gradient(321deg, #6964de -10.33%, #fca6e9 100%)',
        },

        // 边框相关
        '.border-primary': {
          'border-color': '#bf8be5',
        },
        '.border-light-primary': {
          'border-color': 'rgba(139, 92, 246, 0.16)',
        },

        // 文字颜色相关
        '.text-primary': {
          color: 'var(--primary)',
        },
        '.btn-text-color': {
          color: 'var(--neutral-900)',
        },
        '.bg-text-color': {
          color: 'var(--neutral-50)',
        },
      }

      addUtilities(customUtilities)
    },
    // Maze 主题相关工具类插件
    function ({ addUtilities }) {
      const mazeUtilities = {
        '.maze-page-title': {
          color: 'var(--primary-text)',
          'font-size': '3.5rem',
          'line-height': '100%',
          'text-align': 'center',
          'font-weight': '600',
          '@media (min-width: 768px) and (max-width: 1024px)': {
            'font-size': '2.4rem',
          },
        },
        '.maze-box-shadow': {
          'box-shadow':
            '0px 20px 28px 0px rgba(139, 92, 246, 0.36), 0px 76px 52px 0px rgba(139, 92, 246, 0.2)',
        },
        '.maze-bg-primary': {
          background: 'var(--primary-bg)',
        },
        '.maze-primary-text': {
          color: 'var(--primary-text)',
        },
        '.maze-bg-gradient-primary': {
          background:
            'linear-gradient(180deg, rgba(255, 119, 119, 0.8) 0%, rgba(240, 221, 255, 0.8) 100%)',
        },
        '.maze-bg-gradient-disabled': {
          background:
            'linear-gradient(180deg, rgba(93, 126, 255, 0.8) 0%, rgba(255, 213, 213, 0.8) 100%)',
        },
        '.maze-bg-gradient-btn': {
          background:
            'linear-gradient(180deg, rgba(144, 77, 221, 0.4) 0%, rgba(171, 201, 227, 0.4) 100%)',
        },
        '.maze-bg-gradient-card': {
          background:
            'linear-gradient(180deg, rgba(42, 58, 122, 0.6) 0%, rgba(195, 129, 202, 0.6) 100%)',
        },
        '.maze-shoot-btn': {
          border: '6px solid #fff',
          background: 'rgba(255, 255, 255, 0.2)',
          'box-shadow': '0px 0px 10px 0px rgba(0, 0, 0, 0.35)',
          'backdrop-filter': 'blur(5px)',
        },
        '.maze-card-shadow': {
          'box-shadow':
            '0px 0px 20px 4px #000 inset, 0px 0px 20px 0px #000 inset',
        },
        '.maze-event-card-shadow': {
          'box-shadow':
            '0px 0px 40px 10px rgba(104, 106, 255, 0.2), 0px 0px 43.983px 0px #9565ff',
        },
        '.maze-event-card-bg': {
          background:
            'linear-gradient(180deg, rgba(14, 0, 23, 0.5) 0%, rgba(70, 25, 112, 0.5) 100%)',
          'box-shadow':
            '0px 0px 40px 10px rgba(104, 106, 255, 0.2), 0px 0px 43.983px 0px #9565ff',
          'backdrop-filter': 'blur(10px)',
        },
        '.maze-start-btn': {
          background: 'radial-gradient(circle at 40% 40%, #6a00ff, #36006a)',
          'box-shadow':
            'inset 0 0 30px rgba(255, 255, 255, 0.15), 0 10px 25px rgba(160, 67, 241, 0.6), 0 -4px 10px rgba(255, 255, 255, 0.1), inset 0 0 10px rgba(255, 255, 255, 0.2)',
          transition: 'all 0.3s ease',
          position: 'absolute',
          top: 'calc(50% - 80px)',
          left: 'calc(50% - 80px)',
        },
        '.maze-badge-default': {
          background: 'rgba(186, 192, 204, 0.2)',
          'box-shadow': '0px 0px 4.48px 0px rgba(0, 0, 0, 0.4)',
        },
        '.maze-loading-card-shadow': {
          background: 'linear-gradient(180deg, #282828 0%, #000 100%)',
          'box-shadow':
            '0px 0px 20px 0px rgba(142, 101, 230, 0.4), 0px 0px 15px 0px rgba(104, 245, 255, 0.8), 0px 0px 20px 0px #5100ff',
        },
        '.maze-theme-title-bg': {
          background: 'linear-gradient(0deg, #000 0%, rgba(0, 0, 0, 0) 100%)',
        },
      }

      addUtilities(mazeUtilities)
    },
    // Swiper 和其他特殊样式插件
    function ({ addUtilities }) {
      const specialUtilities = {
        '.maze-slide-active-shadow': {
          'box-shadow':
            '0px 0px 20px 0px rgba(142, 101, 230, 0.4), 0px 0px 15px 0px rgba(104, 245, 255, 0.8), 0px 0px 20px 0px #5100ff',
        },
        '.categoryList': {
          border: '2px dashed rgba(255, 255, 255, 0.08)',
        },
        '.categoryItemActive': {
          'border-radius': '99px',
          border: '3px solid #edf0f4',
          background:
            'linear-gradient(180deg, rgba(144, 77, 221, 0.4) 0%, rgba(171, 201, 227, 0.4) 100%)',
          'backdrop-filter': 'blur(5px)',
        },
      }

      addUtilities(specialUtilities)
    },
  ],
}
