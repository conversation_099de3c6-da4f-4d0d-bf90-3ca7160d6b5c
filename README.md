## 开发指南

[详情](https://alidocs.dingtalk.com/i/nodes/D1YKdxGX7EqVQAG94dZNVe4QrZk95AzP)

## 大屏缩放方案

1. ios 系统：采用 scale 缩放，因有 zoom 兼容问题

2. 其他系统：采用 zoom 缩放 （支持模版撑满展示骚操作）

```
pnpm i

pnpm run dev
```

# mainnet

https://mirror.mgru.info/?device-token=eyJhbGciOiJIUzI1NiJ9.eyJkZXZpY2VJZCI6MTIyMywiZGV2aWNlU2Vzc2lvbklkIjo2NjcsIm1lcmNoYW50Tm8iOiIwMTg1MTMwIiwiZGV2aWNlVHlwZUNvZGUiOjIsImlhdCI6MTc0MDAzNzAzNywic3ViIjoiMTIyMyJ9.U0SNDnEgQNQbwXot9x679Id6PoUog4nDr8NX2_LLi6o&token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxMywiaXNzIjoiTWF6ZVN0dWRpby1BUEkiLCJzdWIiOiJNYXplU3R1ZGlvIiwiYXVkIjpbIkFQSSJdLCJleHAiOjE3NTU3NDM0MjksIm5iZiI6MTc1MzE1MTQyOSwiaWF0IjoxNzUzMTUxNDI5LCJqdGkiOiI2ODdlZjdjNWE4MGIwZTE3NDQ2ZTU0ZWUifQ.VtLj6gFFfIErlVV5qkV4E9zwBQa8C7XJwPy3tDGizx_PBzukHFY4nMJroy-NAxaRyvLgCQ0MeDFPZyGr1k3qOQ

```
// device-token 会由app注入到webview cookie中，以上仅用于本地调试

```

http://192.168.3.6:16661/?device-token=eyJhbGciOiJIUzI1NiJ9.eyJkZXZpY2VJZCI6MTIyMywiZGV2aWNlU2Vzc2lvbklkIjo2NjcsIm1lcmNoYW50Tm8iOiIwMTg1MTMwIiwiZGV2aWNlVHlwZUNvZGUiOjIsImlhdCI6MTc0MDAzNzAzNywic3ViIjoiMTIyMyJ9.U0SNDnEgQNQbwXot9x679Id6PoUog4nDr8NX2_LLi6o&token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxMjM5LCJpc3MiOiJNYXplU3R1ZGlvLUFQSSIsInN1YiI6Ik1hemVTdHVkaW8iLCJhdWQiOlsiQVBJIl0sImV4cCI6MTc1NjUyMjE5OCwibmJmIjoxNzUzOTMwMTk4LCJpYXQiOjE3NTM5MzAxOTgsImp0aSI6IjY4OGFkOWQ2OWZiNzI3MTIzZWM1MDQ5MiJ9.TEmweE_yL6q_To4rIMafUYqCb4GSVy0kILwFv6vgDKhObIRq7pIyxfEnf0v_CMojiu91y5Yxpq6eHq-2ZouBKA

```

### 约定
已适配的屏幕（需求不断叠加导致适配维护困难，很多先期未考虑的问题，后抽时间移除hack，统一一下）
  横屏（1920 x 1080）
  竖屏 （1080 x 1920）
  ipad tailwindcss ipad:xxx
  webapp tailwindcss phone:xxx
  iphone isIphone()@ua.ts 来判断，以此来区别webapp，覆盖样式

```
